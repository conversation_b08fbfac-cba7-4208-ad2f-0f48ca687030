<#
.SYNOPSIS
    Configure AIMX service for testing by applying registry settings, starting service, and creating desktop shortcut

.DESCRIPTION
    This script performs the following actions:
    1. Applies registry settings from registry.reg to configure AIMX service parameters
    2. Starts the AIMXSrv service
    3. Creates a desktop shortcut to agenticflow.ps1 for easy testing access

.PARAMETER RegistryFile
    Path to the registry file containing AIMX configuration (defaults to .\registry.reg)

.PARAMETER Force
    Skip confirmation prompts

.EXAMPLE
    .\Start-AIMX-Testing.ps1

    # Use custom registry file
    .\Start-AIMX-Testing.ps1 -RegistryFile "C:\path\to\custom.reg"

    # Skip confirmations
    .\Start-AIMX-Testing.ps1 -Force

.NOTES
    This script requires Administrator privileges. Right-click PowerShell and "Run as Administrator".

#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$RegistryFile = ".\registry.reg",

    [switch]$Force
)

# Configuration
$ServiceName = "AIMXSrv"
$AgenticFlowPath = "C:\Windows\System32\agenticflow.ps1"
$DesktopPath = [Environment]::GetFolderPath([Environment+SpecialFolder]::Desktop)
$ShortcutPath = Join-Path $DesktopPath "AIMX AgenticFlow.lnk"

# Function to get current user context
function Get-CurrentUserContext {
    try {
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
        return @{
            Name = $currentUser.Name
            IsSystem = $currentUser.IsSystem
            IsAdmin = ([Security.Principal.WindowsPrincipal] $currentUser).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
        }
    }
    catch {
        return @{
            Name = "Unknown"
            IsSystem = $false
            IsAdmin = $false
        }
    }
}

# Requires Administrator privileges
#Requires -RunAsAdministrator

# Get current execution context
$userContext = Get-CurrentUserContext

Write-Host "=== AIMX Testing Setup Script ===" -ForegroundColor Green
Write-Host "Configuring AIMX service for testing..." -ForegroundColor Cyan
Write-Host ""
Write-Host "Execution Context:" -ForegroundColor Yellow
Write-Host "  User: $($userContext.Name)" -ForegroundColor Cyan
Write-Host "  Is Admin: $($userContext.IsAdmin)" -ForegroundColor Cyan
Write-Host ""

# Validate registry file exists
if (-not (Test-Path $RegistryFile)) {
    Write-Host "ERROR: Registry file not found: $RegistryFile" -ForegroundColor Red
    exit 1
}

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  Registry File: $RegistryFile" -ForegroundColor Cyan
Write-Host "  Service Name: $ServiceName" -ForegroundColor Cyan
Write-Host "  AgenticFlow Path: $AgenticFlowPath" -ForegroundColor Cyan
Write-Host "  Desktop Shortcut: $ShortcutPath" -ForegroundColor Cyan
Write-Host ""

# Confirmation prompt
if (-not $Force) {
    Write-Host "This script will perform the following actions:" -ForegroundColor Yellow
    Write-Host "  - Apply registry settings from $RegistryFile" -ForegroundColor Cyan
    Write-Host "  - Start the $ServiceName service" -ForegroundColor Cyan
    Write-Host "  - Create desktop shortcut to agenticflow.ps1" -ForegroundColor Cyan
    Write-Host ""

    $confirmation = Read-Host "Proceed with AIMX testing setup? (y/N)"
    if ($confirmation -notmatch '^[Yy]') {
        Write-Host "Setup cancelled." -ForegroundColor Yellow
        exit 0
    }
    Write-Host ""
}

# Function to apply registry settings using PowerShell commands
function Set-AIMXRegistrySettings {
    param([string]$RegFilePath)
    
    Write-Host "Applying AIMX registry settings..." -ForegroundColor Yellow
    
    try {
        # Read the registry file content
        $regContent = Get-Content $RegFilePath
        
        # Parse the registry file and extract the settings
        $registryPath = "HKLM:\SYSTEM\CurrentControlSet\Services\AIMXSrv\Parameters"
        
        # Ensure the registry path exists
        if (-not (Test-Path $registryPath)) {
            Write-Host "Creating registry path: $registryPath" -ForegroundColor Yellow
            New-Item -Path $registryPath -Force | Out-Null
        }
        
        # Apply each registry setting from the file
        $settingsApplied = 0
        
        foreach ($line in $regContent) {
            # Skip empty lines and comments
            if ($line -match '^\s*$' -or $line -match '^Windows Registry Editor' -or $line -match '^\[') {
                continue
            }
            
            # Parse registry value lines (format: "ValueName"="ValueData")
            if ($line -match '^"([^"]+)"="([^"]*)"') {
                $valueName = $matches[1]
                $valueData = $matches[2]
                
                try {
                    Set-ItemProperty -Path $registryPath -Name $valueName -Value $valueData -Type String
                    Write-Host "Set registry value: $valueName = $valueData" -ForegroundColor Green
                    $settingsApplied++
                }
                catch {
                    Write-Host "WARNING: Failed to set registry value $valueName`: $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        }
        
        if ($settingsApplied -gt 0) {
            Write-Host "Successfully applied $settingsApplied registry setting(s)." -ForegroundColor Green
            return $true
        } else {
            Write-Host "WARNING: No registry settings were applied." -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "ERROR: Failed to apply registry settings: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to start the AIMX service
function Start-AIMXService {
    Write-Host "Starting $ServiceName service..." -ForegroundColor Yellow
    
    try {
        # Check if service exists
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if (-not $service) {
            Write-Host "ERROR: $ServiceName service not found. Please ensure AIMX is properly installed." -ForegroundColor Red
            return $false
        }
        
        # Check current service status
        if ($service.Status -eq 'Running') {
            Write-Host "$ServiceName service is already running." -ForegroundColor Green
            return $true
        }
        
        # Start the service
        $result = & net start $ServiceName 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "$ServiceName service started successfully." -ForegroundColor Green
            
            # Wait for service to fully start
            $timeout = 30
            $elapsed = 0
            while ($elapsed -lt $timeout) {
                $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
                if ($service -and $service.Status -eq 'Running') {
                    break
                }
                Start-Sleep -Seconds 1
                $elapsed++
            }
            
            if ($elapsed -ge $timeout) {
                Write-Host "WARNING: Service may not have started properly within $timeout seconds" -ForegroundColor Yellow
            }
            
            return $true
        } else {
            Write-Host "ERROR: Failed to start service: $result" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "ERROR: Failed to start service: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to create desktop shortcut
function New-AgenticFlowShortcut {
    Write-Host "Creating desktop shortcut to AgenticFlow..." -ForegroundColor Yellow
    
    try {
        # Check if agenticflow.ps1 exists
        if (-not (Test-Path $AgenticFlowPath)) {
            Write-Host "WARNING: AgenticFlow script not found at: $AgenticFlowPath" -ForegroundColor Yellow
            Write-Host "Shortcut will still be created but may not work until the file exists." -ForegroundColor Yellow
        }
        
        # Create WScript.Shell COM object for shortcut creation
        $WshShell = New-Object -ComObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
        
        # Configure shortcut properties
        $Shortcut.TargetPath = "powershell.exe"
        $Shortcut.Arguments = "-ExecutionPolicy Bypass -File `"$AgenticFlowPath`""
        $Shortcut.WorkingDirectory = "C:\Windows\System32"
        $Shortcut.Description = "AIMX AgenticFlow Testing Script"
        $Shortcut.IconLocation = "powershell.exe,0"
        
        # Save the shortcut
        $Shortcut.Save()
        
        Write-Host "Desktop shortcut created successfully: $ShortcutPath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "ERROR: Failed to create desktop shortcut: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Step 1: Apply registry settings
Write-Host "=== Step 1: Applying Registry Settings ===" -ForegroundColor Magenta
$registrySuccess = Set-AIMXRegistrySettings -RegFilePath $RegistryFile
Write-Host ""

# Step 2: Start the AIMX service
Write-Host "=== Step 2: Starting AIMX Service ===" -ForegroundColor Magenta
$serviceSuccess = Start-AIMXService
Write-Host ""

# Step 3: Create desktop shortcut
Write-Host "=== Step 3: Creating Desktop Shortcut ===" -ForegroundColor Magenta
$shortcutSuccess = New-AgenticFlowShortcut
Write-Host ""

# Summary
Write-Host "=== Setup Summary ===" -ForegroundColor Green
Write-Host ""
if ($registrySuccess -and $serviceSuccess -and $shortcutSuccess) {
    Write-Host "AIMX testing setup completed successfully!" -ForegroundColor Green
} else {
    Write-Host "AIMX testing setup completed with some issues. Please review the output above." -ForegroundColor Yellow
}
Write-Host ""
Write-Host "Setup Results:" -ForegroundColor Yellow
Write-Host "  Registry Settings: $(if ($registrySuccess) { "Applied" } else { "Failed" })" -ForegroundColor $(if ($registrySuccess) { "Green" } else { "Red" })
Write-Host "  Service Status: $(if ($serviceSuccess) { "Started" } else { "Failed" })" -ForegroundColor $(if ($serviceSuccess) { "Green" } else { "Red" })
Write-Host "  Desktop Shortcut: $(if ($shortcutSuccess) { "Created" } else { "Failed" })" -ForegroundColor $(if ($shortcutSuccess) { "Green" } else { "Red" })
Write-Host ""

Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Verify service is running: Get-Service $ServiceName" -ForegroundColor Cyan
Write-Host "2. Check registry settings: Get-ItemProperty 'HKLM:\SYSTEM\CurrentControlSet\Services\AIMXSrv\Parameters'" -ForegroundColor Cyan
Write-Host "3. Double-click the desktop shortcut to test AgenticFlow" -ForegroundColor Cyan
Write-Host "4. Monitor service logs for any issues" -ForegroundColor Cyan
Write-Host ""

if ($serviceSuccess) {
    Write-Host "AIMX service is now ready for testing!" -ForegroundColor Green
} else {
    Write-Host "Please resolve service issues before testing." -ForegroundColor Yellow
}
