<#
.SYNOPSIS
    Complete AIMX uninstallation script that removes all components installed by Deploy-AIMX.ps1

.DESCRIPTION
    This script handles the complete AIMX uninstallation process:
    1. Runs with Administrator privileges
    2. Stops and uninstalls AIMX service
    3. Removes AIMX files from system locations
    4. Removes PowerShell module configuration
    5. Removes strong name verification bypass
    6. Optionally removes .NET 8.0 Runtime
    7. Cleans up backup files

.PARAMETER RemoveDotNet
    Also remove .NET 8.0 Runtime (use with caution as other applications may depend on it)

.PARAMETER KeepBackups
    Keep backup files created during installation

.PARAMETER Force
    Skip confirmation prompts

.EXAMPLE
    .\Uninstall-AIMX.ps1

    # Remove .NET Runtime as well
    .\Uninstall-AIMX.ps1 -RemoveDotNet

    # Keep backup files
    .\Uninstall-AIMX.ps1 -KeepBackups

.NOTES
    This script requires Administrator privileges. Right-click PowerShell and "Run as Administrator".

#>

[CmdletBinding()]
param(
    [switch]$RemoveDotNet,
    [switch]$KeepBackups,
    [switch]$Force
)

# Configuration (matching Deploy-AIMX.ps1)
$ServiceName = "AIMXSrv"
$ProcessName = "netragservice"

# Function to get current user context
function Get-CurrentUserContext {
    try {
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
        return @{
            Name = $currentUser.Name
            IsSystem = $currentUser.IsSystem
            IsAdmin = ([Security.Principal.WindowsPrincipal] $currentUser).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
        }
    }
    catch {
        return @{
            Name = "Unknown"
            IsSystem = $false
            IsAdmin = $false
        }
    }
}

# Requires Administrator privileges
#Requires -RunAsAdministrator

# Get current execution context
$userContext = Get-CurrentUserContext

# System paths using environment variables
$System32Path = [Environment]::GetFolderPath([Environment+SpecialFolder]::System)
$ProgramDataPath = [Environment]::GetFolderPath([Environment+SpecialFolder]::CommonApplicationData)

$AimxProgramDataPath = Join-Path $ProgramDataPath "Microsoft\AIMX"
$PowerShellProfilePath = Join-Path $System32Path "WindowsPowerShell\v1.0\profile.ps1"
$AimxModulePath = Join-Path $System32Path "aimx.psd1"

Write-Host "=== AIMX Complete Uninstallation Script ===" -ForegroundColor Red
Write-Host "Removing AIMX services, files, and configuration..." -ForegroundColor Yellow
Write-Host ""
Write-Host "Execution Context:" -ForegroundColor Yellow
Write-Host "  User: $($userContext.Name)" -ForegroundColor Cyan
Write-Host "  Is Admin: $($userContext.IsAdmin)" -ForegroundColor Cyan
Write-Host ""

Write-Host "Uninstallation Configuration:" -ForegroundColor Yellow
Write-Host "  System32 Path: $System32Path" -ForegroundColor Cyan
Write-Host "  ProgramData Path: $AimxProgramDataPath" -ForegroundColor Cyan
Write-Host "  PowerShell Profile: $PowerShellProfilePath" -ForegroundColor Cyan
Write-Host "  Remove .NET Runtime: $RemoveDotNet" -ForegroundColor Cyan
Write-Host "  Keep Backups: $KeepBackups" -ForegroundColor Cyan
Write-Host ""

# Confirmation prompt
if (-not $Force) {
    Write-Host "This script will perform the following actions:" -ForegroundColor Yellow
    Write-Host "  - Stop and uninstall AIMX service" -ForegroundColor Red
    Write-Host "  - Remove AIMX files from System32" -ForegroundColor Red
    Write-Host "  - Remove AIMX files from ProgramData" -ForegroundColor Red
    Write-Host "  - Remove PowerShell module configuration" -ForegroundColor Red
    Write-Host "  - Remove strong name verification bypass" -ForegroundColor Red
    if ($RemoveDotNet) {
        Write-Host "  - Remove .NET 8.0 Runtime" -ForegroundColor Red
    }
    if (-not $KeepBackups) {
        Write-Host "  - Remove backup files" -ForegroundColor Red
    }
    Write-Host ""

    $confirmation = Read-Host "Proceed with AIMX uninstallation? (y/N)"
    if ($confirmation -notmatch '^[Yy]') {
        Write-Host "Uninstallation cancelled." -ForegroundColor Yellow
        exit 0
    }
    Write-Host ""
}

# Function to stop and uninstall service
function Remove-AimxService {
    Write-Host "Checking AIMX service..." -ForegroundColor Yellow
    
    try {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($service) {
            # Stop service if running
            if ($service.Status -eq 'Running') {
                Write-Host "Stopping $ServiceName service..." -ForegroundColor Yellow
                
                $result = & net stop $ServiceName 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "$ServiceName service stopped successfully." -ForegroundColor Green
                } else {
                    Write-Host "WARNING: Failed to stop service: $result" -ForegroundColor Yellow
                }
                
                # Wait for service to stop
                $timeout = 30
                $elapsed = 0
                while ($elapsed -lt $timeout) {
                    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
                    if (-not $service -or $service.Status -eq 'Stopped') {
                        break
                    }
                    Start-Sleep -Seconds 1
                    $elapsed++
                }
            }
            
            # Uninstall service
            Write-Host "Uninstalling $ServiceName service..." -ForegroundColor Yellow
            $deleteResult = & sc.exe delete $ServiceName 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "$ServiceName service uninstalled successfully." -ForegroundColor Green
            } else {
                Write-Host "WARNING: Failed to uninstall service: $deleteResult" -ForegroundColor Yellow
            }
        } else {
            Write-Host "$ServiceName service not found." -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "WARNING: Error managing service: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Function to kill processes
function Stop-NetRagProcess {
    Write-Host "Checking for $ProcessName.exe processes..." -ForegroundColor Yellow
    
    try {
        $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Host "Found $($processes.Count) $ProcessName.exe process(es). Terminating..." -ForegroundColor Yellow
            
            foreach ($process in $processes) {
                try {
                    $process.Kill()
                    $process.WaitForExit(5000)
                    Write-Host "Process $($process.Id) terminated successfully." -ForegroundColor Green
                }
                catch {
                    Write-Host "WARNING: Failed to terminate process $($process.Id): $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Host "No $ProcessName.exe processes found." -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "WARNING: Error checking for processes: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Function to remove strong name verification bypass
function Remove-StrongNameBypass {
    Write-Host "Removing strong name verification bypass..." -ForegroundColor Yellow

    # Local sn.exe path
    $dotnetFolder = Join-Path $PSScriptRoot "dotnet"
    $snExePath = Join-Path $dotnetFolder "sn.exe"

    # Check if local sn.exe exists
    if (-not (Test-Path $snExePath)) {
        Write-Host "WARNING: sn.exe not found at: $snExePath" -ForegroundColor Yellow
        Write-Host "Strong name verification bypass removal will be skipped." -ForegroundColor Yellow
        return $true
    }

    try {
        # AIMX assembly files that had strong name bypass
        $system32Path = [Environment]::GetFolderPath([Environment+SpecialFolder]::System)
        $aimxAssemblies = @(
            (Join-Path $system32Path "aimxpsh.dll")
        )

        $bypassRemoved = $false

        foreach ($assemblyPath in $aimxAssemblies) {
            $assemblyName = Split-Path $assemblyPath -Leaf

            try {
                # Remove strong name verification bypass
                $snResult = & $snExePath -Vu $assemblyPath 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "Strong name verification bypass removed for: $assemblyName" -ForegroundColor Green
                    $bypassRemoved = $true
                } else {
                    Write-Host "Note: Could not remove bypass for $assemblyName (may not have been configured): $snResult" -ForegroundColor Gray
                }
            }
            catch {
                Write-Host "Note: Could not remove bypass for $assemblyName" -ForegroundColor Gray
            }
        }

        if ($bypassRemoved) {
            Write-Host "Strong name verification bypass removed successfully." -ForegroundColor Green
        } else {
            Write-Host "Note: No strong name verification bypass found to remove." -ForegroundColor Gray
        }

        return $true
    }
    catch {
        Write-Host "WARNING: Failed to remove strong name verification bypass" -ForegroundColor Yellow
        return $false
    }
}

# Step 1: Stop services and processes
Write-Host "=== Step 1: Stopping Services and Processes ===" -ForegroundColor Magenta
Remove-AimxService
Stop-NetRagProcess
Write-Host ""

# Step 2: Remove PowerShell module configuration
Write-Host "=== Step 2: Removing PowerShell Module Configuration ===" -ForegroundColor Magenta
try {
    if (Test-Path $PowerShellProfilePath) {
        $profileContent = Get-Content $PowerShellProfilePath -Raw
        $importStatement = "Import-Module `"$AimxModulePath`""
        
        if ($profileContent -match [regex]::Escape($importStatement)) {
            # Create backup if not keeping backups
            if (-not $KeepBackups) {
                $backupPath = $PowerShellProfilePath + ".uninstall-backup." + (Get-Date -Format "yyyyMMdd-HHmmss")
                Copy-Item $PowerShellProfilePath $backupPath -Force
                Write-Host "Backed up PowerShell profile to: $backupPath" -ForegroundColor Gray
            }
            
            # Remove the import statement
            $updatedContent = $profileContent -replace [regex]::Escape($importStatement + "`n"), ""
            $updatedContent = $updatedContent -replace [regex]::Escape($importStatement), ""
            
            $updatedContent | Set-Content $PowerShellProfilePath -Encoding UTF8
            Write-Host "Removed AIMX module import from PowerShell profile." -ForegroundColor Green
        } else {
            Write-Host "AIMX module import not found in PowerShell profile." -ForegroundColor Gray
        }
    } else {
        Write-Host "PowerShell profile not found." -ForegroundColor Gray
    }
}
catch {
    Write-Host "ERROR: Failed to update PowerShell profile: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Step 3: Remove strong name verification bypass
Write-Host "=== Step 3: Removing Strong Name Verification Bypass ===" -ForegroundColor Magenta
$bypassSuccess = Remove-StrongNameBypass
if (-not $bypassSuccess) {
    Write-Host "Strong name bypass removal had issues but continuing..." -ForegroundColor Yellow
}
Write-Host ""

# Step 4: Remove AIMX files from System32
Write-Host "=== Step 4: Removing AIMX Files from System32 ===" -ForegroundColor Magenta
Write-Host "Removing AIMX files from System32..." -ForegroundColor Yellow

try {
    # List of AIMX files that were copied to System32
    $aimxSystemFiles = @(
        "aimx.psd1",
        "aimxpsh.dll",
        "Aimx.ServiceInstaller.exe",
        "netrag.exe",
        "netrag.exe.config"
        # Add other files that were deployed to System32
    )

    $filesRemoved = 0
    $filesNotFound = 0

    foreach ($fileName in $aimxSystemFiles) {
        $filePath = Join-Path $System32Path $fileName

        if (Test-Path $filePath) {
            try {
                Remove-Item $filePath -Force
                Write-Host "Removed: $fileName" -ForegroundColor Green
                $filesRemoved++
            }
            catch {
                Write-Host "WARNING: Failed to remove $fileName`: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "Not found: $fileName" -ForegroundColor Gray
            $filesNotFound++
        }
    }

    Write-Host "System32 cleanup completed. Removed: $filesRemoved, Not found: $filesNotFound" -ForegroundColor Green
}
catch {
    Write-Host "ERROR: Failed to remove System32 files: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Step 5: Remove AIMX files from ProgramData
Write-Host "=== Step 5: Removing AIMX Files from ProgramData ===" -ForegroundColor Magenta
Write-Host "Removing AIMX files from ProgramData..." -ForegroundColor Yellow

try {
    if (Test-Path $AimxProgramDataPath) {
        # Create backup if keeping backups
        if ($KeepBackups) {
            $backupPath = $AimxProgramDataPath + ".uninstall-backup." + (Get-Date -Format "yyyyMMdd-HHmmss")
            Write-Host "Creating backup of ProgramData at: $backupPath" -ForegroundColor Gray
            Copy-Item $AimxProgramDataPath $backupPath -Recurse -Force
        }

        Remove-Item $AimxProgramDataPath -Recurse -Force
        Write-Host "Removed AIMX ProgramData directory: $AimxProgramDataPath" -ForegroundColor Green
    } else {
        Write-Host "AIMX ProgramData directory not found: $AimxProgramDataPath" -ForegroundColor Gray
    }
}
catch {
    Write-Host "ERROR: Failed to remove ProgramData files: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Step 6: Remove .NET 8.0 Runtime (optional)
if ($RemoveDotNet) {
    Write-Host "=== Step 6: Removing .NET 8.0 Runtime ===" -ForegroundColor Magenta
    Write-Host "WARNING: Removing .NET 8.0 Runtime may affect other applications!" -ForegroundColor Red

    if (-not $Force) {
        $dotnetConfirm = Read-Host "Are you sure you want to remove .NET 8.0 Runtime? (y/N)"
        if ($dotnetConfirm -notmatch '^[Yy]') {
            Write-Host ".NET 8.0 Runtime removal skipped." -ForegroundColor Yellow
        } else {
            Write-Host "Attempting to uninstall .NET 8.0 Runtime..." -ForegroundColor Yellow

            try {
                # Try to find and uninstall .NET 8.0 Runtime
                $uninstallKeys = @(
                    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
                    "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*"
                )

                $dotnetPrograms = Get-ItemProperty $uninstallKeys -ErrorAction SilentlyContinue |
                    Where-Object { $_.DisplayName -like "*Microsoft .NET Runtime - 8.*" -or
                                   $_.DisplayName -like "*Microsoft ASP.NET Core*8.*" }

                if ($dotnetPrograms) {
                    foreach ($program in $dotnetPrograms) {
                        Write-Host "Found: $($program.DisplayName)" -ForegroundColor Cyan
                        if ($program.UninstallString) {
                            Write-Host "Uninstalling: $($program.DisplayName)" -ForegroundColor Yellow
                            $uninstallCmd = $program.UninstallString -replace "MsiExec.exe", "" -replace "/I", "/X"
                            Start-Process "MsiExec.exe" -ArgumentList "$uninstallCmd /quiet" -Wait
                            Write-Host "Uninstalled: $($program.DisplayName)" -ForegroundColor Green
                        }
                    }
                } else {
                    Write-Host ".NET 8.0 Runtime not found in installed programs." -ForegroundColor Gray
                }
            }
            catch {
                Write-Host "ERROR: Failed to uninstall .NET 8.0 Runtime: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    Write-Host ""
} else {
    Write-Host "=== Step 6: .NET 8.0 Runtime (Skipped) ===" -ForegroundColor Magenta
    Write-Host ".NET 8.0 Runtime removal skipped (use -RemoveDotNet to remove)." -ForegroundColor Gray
    Write-Host ""
}

# Step 7: Clean up backup files (optional)
if (-not $KeepBackups) {
    Write-Host "=== Step 7: Cleaning Up Backup Files ===" -ForegroundColor Magenta
    Write-Host "Removing backup files..." -ForegroundColor Yellow

    try {
        # Find and remove backup files created during installation
        $backupPatterns = @(
            "$PowerShellProfilePath.backup.*",
            "$AimxProgramDataPath.backup.*",
            "$AimxProgramDataPath.uninstall-backup.*"
        )

        $backupsRemoved = 0
        foreach ($pattern in $backupPatterns) {
            $backupFiles = Get-ChildItem $pattern -ErrorAction SilentlyContinue
            foreach ($backupFile in $backupFiles) {
                try {
                    Remove-Item $backupFile.FullName -Recurse -Force
                    Write-Host "Removed backup: $($backupFile.Name)" -ForegroundColor Green
                    $backupsRemoved++
                }
                catch {
                    Write-Host "WARNING: Failed to remove backup $($backupFile.Name): $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        }

        if ($backupsRemoved -eq 0) {
            Write-Host "No backup files found to remove." -ForegroundColor Gray
        } else {
            Write-Host "Removed $backupsRemoved backup file(s)." -ForegroundColor Green
        }
    }
    catch {
        Write-Host "ERROR: Failed to clean up backup files: $($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host ""
} else {
    Write-Host "=== Step 7: Backup Files (Preserved) ===" -ForegroundColor Magenta
    Write-Host "Backup files preserved as requested." -ForegroundColor Gray
    Write-Host ""
}

# Summary
Write-Host "=== Uninstallation Summary ===" -ForegroundColor Green
Write-Host ""
Write-Host "AIMX uninstallation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Uninstallation Details:" -ForegroundColor Yellow
Write-Host "  Service: Stopped and uninstalled" -ForegroundColor Green
Write-Host "  System32 files: Removed" -ForegroundColor Green
Write-Host "  ProgramData files: Removed" -ForegroundColor Green
Write-Host "  PowerShell module: Removed from profile" -ForegroundColor Green
Write-Host "  Strong name bypass: Removed" -ForegroundColor Green
if ($RemoveDotNet) {
    Write-Host "  .NET 8.0 Runtime: Removal attempted" -ForegroundColor Yellow
} else {
    Write-Host "  .NET 8.0 Runtime: Preserved" -ForegroundColor Gray
}
if ($KeepBackups) {
    Write-Host "  Backup files: Preserved" -ForegroundColor Gray
} else {
    Write-Host "  Backup files: Removed" -ForegroundColor Green
}
Write-Host ""

Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart PowerShell to ensure module is unloaded" -ForegroundColor Cyan
Write-Host "2. Verify no AIMX processes are running: Get-Process *aimx*, Get-Process netrag" -ForegroundColor Cyan
Write-Host "3. Check that AIMX service is removed: Get-Service AIMXSrv" -ForegroundColor Cyan
if (-not $RemoveDotNet) {
    Write-Host "4. Optionally remove .NET 8.0 Runtime manually if no longer needed" -ForegroundColor Cyan
}
Write-Host ""

Write-Host "AIMX uninstallation completed successfully!" -ForegroundColor Green
